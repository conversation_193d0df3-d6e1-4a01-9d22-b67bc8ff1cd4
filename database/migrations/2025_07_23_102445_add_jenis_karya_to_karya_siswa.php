<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('karya_siswa', function (Blueprint $table) {
            $table->string('jenis_karya')->nullable()->after('judul_karya');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('karya_siswa', function (Blueprint $table) {
            $table->dropColumn('jenis_karya');
        });
    }
};
