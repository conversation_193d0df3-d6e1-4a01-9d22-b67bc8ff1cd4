<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Kegiatan extends Model
{
    protected $table = 'kegiatan';

    protected $fillable = [
        'judul_kegiatan',
        'deskripsi_kegiatan',
        'tanggal_kegiatan',
        'jenis_kegiatan',
        'gambar',
        'lokasi',
        'user_id'
    ];

    protected $casts = [
        'tanggal_kegiatan' => 'date',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
