import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, usePage, router } from '@inertiajs/react';
import DataTable, { TableColumn } from 'react-data-table-component';
import { Search, Edit, Trash2, BookOpen, X, Upload } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Karya Siswa', href: '/karya/siswa' }];

// Type definition for student work data
type KaryaSiswa = {
    id: number;
    nama_siswa: string;
    kelas: string;
    judul_karya: string;
    jenis_karya: string;
    tanggal_dibuat: string;
    foto?: string;
};

const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
            fontSize: '14px',
            backgroundColor: '#f9fafb',
        },
    },
    rows: {
        style: {
            minHeight: '48px',
        },
    },
};

interface KaryaPageProps {
    karya: KaryaSiswa[];
    [key: string]: any;
}

export default function Karya() {
    const { karya = [] } = usePage<KaryaPageProps>().props;
    const [data, setData] = useState<KaryaSiswa[]>(karya);
    const [search, setSearch] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [formData, setFormData] = useState({
        nama_siswa: '',
        kelas: '',
        judul_karya: '',
        jenis_karya: '',
        tanggal_dibuat: '',
        foto: null as File | null
    });

    // Function to handle delete karya
    const handleDelete = (id: number) => {
        if (confirm('Apakah Anda yakin ingin menghapus karya ini?')) {
            router.delete(`/delete/karya/${id}`, {
                onSuccess: () => {
                    // Remove from local state
                    setData(data.filter(item => item.id !== id));
                },
                onError: (errors) => {
                    console.error('Error deleting karya:', errors);
                    alert('Gagal menghapus karya siswa.');
                }
            });
        }
    };

    // Form handling functions
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        setFormData(prev => ({
            ...prev,
            foto: file
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        const submitData = new FormData();
        submitData.append('nama_siswa', formData.nama_siswa);
        submitData.append('kelas', formData.kelas);
        submitData.append('judul_karya', formData.judul_karya);
        submitData.append('jenis_karya', formData.jenis_karya);
        submitData.append('tanggal_dibuat', formData.tanggal_dibuat);

        if (formData.foto) {
            submitData.append('foto', formData.foto);
        }

        router.post('/add/karya', submitData, {
            forceFormData: true,
            onSuccess: () => {
                setIsModalOpen(false);
                setFormData({
                    nama_siswa: '',
                    kelas: '',
                    judul_karya: '',
                    jenis_karya: '',
                    tanggal_dibuat: '',
                    foto: null
                });
                // Refresh data
                window.location.reload();
            },
            onError: (errors) => {
                console.error('Form errors:', errors);
                alert('Gagal menambahkan karya siswa.');
            }
        });
    };

    // Filter data based on search
    const filteredData = data.filter(karya =>
        karya.nama_siswa.toLowerCase().includes(search.toLowerCase()) ||
        karya.judul_karya.toLowerCase().includes(search.toLowerCase()) ||
        karya.kelas.toLowerCase().includes(search.toLowerCase()) ||
        karya.jenis_karya.toLowerCase().includes(search.toLowerCase())
    );

    const columns: TableColumn<KaryaSiswa>[] = [
        {
            name: 'No',
            cell: (_row, index) => index + 1,
            width: '60px',
        },
        {
            name: 'Nama Siswa',
            selector: (row) => row.nama_siswa,
            sortable: true,
        },
        {
            name: 'Kelas',
            selector: (row) => row.kelas,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Judul Karya',
            selector: (row) => row.judul_karya,
            sortable: true,
            wrap: true,
        },
        {
            name: 'Jenis',
            selector: (row) => row.jenis_karya,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Tanggal',
            cell: (row) => (
                <span className="text-sm">
                    {new Date(row.tanggal_dibuat).toLocaleDateString('id-ID')}
                </span>
            ),
            sortable: true,
            selector: (row) => row.tanggal_dibuat,
            width: '120px',
        },
        {
            name: 'Aksi',
            cell: (row) => (
                <div className="flex gap-2">
                    <button className="text-sm text-blue-600 hover:underline">
                        Edit
                    </button>
                    <button
                        className="text-sm text-red-600 hover:underline"
                        onClick={() => handleDelete(row.id)}
                    >
                        Hapus
                    </button>
                </div>
            ),
            width: '120px',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Karya Siswa" />
            <div className="p-4 space-y-4">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold">Data Karya Siswa</h2>
                    <button
                        onClick={() => setIsModalOpen(true)}
                        className="rounded-md bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700 shadow-sm"
                    >
                        + Tambah Karya
                    </button>
                </div>

                {/* Search */}
                <div className="flex gap-4">
                    <div className="flex-1 relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <input
                            type="text"
                            placeholder="Cari nama siswa, judul karya, kelas..."
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>
                </div>

                {/* Table */}
                <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-md dark:border-gray-700 dark:bg-gray-900">
                    <DataTable
                        columns={columns}
                        data={filteredData}
                        pagination
                        responsive
                        highlightOnHover
                        striped
                        customStyles={customStyles}
                        noDataComponent="Data tidak ditemukan."
                    />
                </div>

                {/* Modal Form Tambah Karya */}
                {isModalOpen && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 px-4">
                        <div className="w-full max-w-2xl rounded-xl bg-white p-6 shadow-lg dark:bg-gray-800 max-h-[90vh] overflow-y-auto">
                            {/* Modal Header */}
                            <div className="flex items-center justify-between mb-6">
                                <div className="flex items-center space-x-2">
                                    <BookOpen className="w-5 h-5 text-blue-600" />
                                    <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Tambah Karya Siswa</h2>
                                </div>
                                <button
                                    onClick={() => setIsModalOpen(false)}
                                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>

                            {/* Form */}
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {/* Nama Siswa */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                                            Nama Siswa *
                                        </label>
                                        <input
                                            type="text"
                                            name="nama_siswa"
                                            value={formData.nama_siswa}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                            placeholder="Masukkan nama siswa"
                                        />
                                    </div>

                                    {/* Kelas */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                                            Kelas *
                                        </label>
                                        <input
                                            type="text"
                                            name="kelas"
                                            value={formData.kelas}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                            placeholder="Contoh: 10 IPA 1"
                                        />
                                    </div>
                                </div>

                                {/* Judul Karya */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                                        Judul Karya *
                                    </label>
                                    <input
                                        type="text"
                                        name="judul_karya"
                                        value={formData.judul_karya}
                                        onChange={handleInputChange}
                                        required
                                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                        placeholder="Masukkan judul karya"
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {/* Jenis Karya */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                                            Jenis Karya *
                                        </label>
                                        <select
                                            name="jenis_karya"
                                            value={formData.jenis_karya}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                        >
                                            <option value="">Pilih jenis karya</option>
                                            <option value="Lukisan">Lukisan</option>
                                            <option value="Kerajinan">Kerajinan</option>
                                            <option value="Tulisan">Tulisan</option>
                                            <option value="Karya Ilmiah">Karya Ilmiah</option>
                                            <option value="Desain">Desain</option>
                                            <option value="Lainnya">Lainnya</option>
                                        </select>
                                    </div>

                                    {/* Tanggal Dibuat */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                                            Tanggal Dibuat *
                                        </label>
                                        <input
                                            type="date"
                                            name="tanggal_dibuat"
                                            value={formData.tanggal_dibuat}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                        />
                                    </div>
                                </div>

                                {/* Upload Foto */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                                        Foto Karya
                                    </label>
                                    <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                                        <div className="space-y-1 text-center">
                                            <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                            <div className="flex text-sm text-gray-600">
                                                <label
                                                    htmlFor="foto"
                                                    className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                                                >
                                                    <span>Upload foto</span>
                                                    <input
                                                        id="foto"
                                                        name="foto"
                                                        type="file"
                                                        className="sr-only"
                                                        accept="image/*"
                                                        onChange={handleFileChange}
                                                    />
                                                </label>
                                                <p className="pl-1">atau drag and drop</p>
                                            </div>
                                            <p className="text-xs text-gray-500">PNG, JPG, GIF maksimal 2MB</p>
                                            {formData.foto && (
                                                <p className="text-xs text-blue-600 mt-2">
                                                    File terpilih: {formData.foto.name}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Form Actions */}
                                <div className="flex justify-end space-x-3 pt-4">
                                    <button
                                        type="button"
                                        onClick={() => setIsModalOpen(false)}
                                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                                    >
                                        Batal
                                    </button>
                                    <button
                                        type="submit"
                                        className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                                    >
                                        Simpan Karya
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}