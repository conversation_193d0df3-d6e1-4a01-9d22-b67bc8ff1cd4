import { useState, useEffect } from 'react';
import { usePage, router } from '@inertiajs/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    ArrowLeft,
    Calendar,
    MapPin,
    User,
    Users,
    GraduationCap,
    Globe,
    Filter,
    Search,
    Clock,
    Eye,
    Share2
} from 'lucide-react';

interface KegiatanData {
    id: number;
    nama_kegiatan: string;
    deskripsi: string;
    tanggal_kegiatan: string;
    jenis_kegiatan: 'siswa' | 'guru' | 'umum';
    foto: string | null;
    lokasi: string;
    author: string;
    created_at: string;
}

interface PageProps {
    kegiatan: KegiatanData[];
    [key: string]: any;
}

export default function PageKegiatan() {
    const { props } = usePage<PageProps>();
    const { kegiatan } = props;

    const [filteredKegiatan, setFilteredKegiatan] = useState<KegiatanData[]>(kegiatan);
    const [selectedFilter, setSelectedFilter] = useState<string>('semua');
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedKegiatan, setSelectedKegiatan] = useState<KegiatanData | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    // Filter and search functionality
    useEffect(() => {
        let filtered = kegiatan;

        // Filter by type
        if (selectedFilter !== 'semua') {
            filtered = filtered.filter(item => item.jenis_kegiatan === selectedFilter);
        }

        // Filter by search query
        if (searchQuery) {
            filtered = filtered.filter(item =>
                item.nama_kegiatan.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.deskripsi.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.lokasi.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        setFilteredKegiatan(filtered);
    }, [selectedFilter, searchQuery, kegiatan]);

    const handleGoBack = () => {
        router.visit('/');
    };

    const getJenisIcon = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return <Users className="w-5 h-5 text-blue-600" />;
            case 'guru':
                return <GraduationCap className="w-5 h-5 text-green-600" />;
            default:
                return <Globe className="w-5 h-5 text-purple-600" />;
        }
    };

    const getJenisColor = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'guru':
                return 'bg-green-100 text-green-800 border-green-200';
            default:
                return 'bg-purple-100 text-purple-800 border-purple-200';
        }
    };

    const openModal = (kegiatan: KegiatanData) => {
        setSelectedKegiatan(kegiatan);
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setSelectedKegiatan(null);
        setIsModalOpen(false);
    };

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.5
            }
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
            {/* Header */}
            <div className="bg-gradient-to-r from-green-900 to-green-800 text-white">
                <div className="max-w-7xl mx-auto px-6 py-8">
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-4 mb-6"
                    >
                        <button
                            onClick={handleGoBack}
                            className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
                        >
                            <ArrowLeft className="w-5 h-5" />
                            Kembali
                        </button>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="text-center"
                    >
                        <h1 className="text-4xl font-bold mb-4">Kegiatan Sekolah</h1>
                        <p className="text-xl text-green-100 max-w-3xl mx-auto">
                            Berbagai kegiatan dan aktivitas yang dilakukan oleh guru dan siswa MTs Negeri 4 Gunungkidul
                        </p>
                    </motion.div>
                </div>
            </div>

            {/* Filters and Search */}
            <div className="max-w-7xl mx-auto px-6 py-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-white rounded-2xl shadow-lg p-6 mb-8"
                >
                    <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
                        {/* Search */}
                        <div className="relative flex-1 max-w-md">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <input
                                type="text"
                                placeholder="Cari kegiatan..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            />
                        </div>

                        {/* Filter Buttons */}
                        <div className="flex gap-2 flex-wrap">
                            {[
                                { key: 'semua', label: 'Semua', icon: Globe },
                                { key: 'siswa', label: 'Kegiatan Siswa', icon: Users },
                                { key: 'guru', label: 'Kegiatan Guru', icon: GraduationCap }
                            ].map(({ key, label, icon: Icon }) => (
                                <button
                                    key={key}
                                    onClick={() => setSelectedFilter(key)}
                                    className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all ${
                                        selectedFilter === key
                                            ? 'bg-green-600 text-white shadow-lg'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                    }`}
                                >
                                    <Icon className="w-4 h-4" />
                                    {label}
                                </button>
                            ))}
                        </div>
                    </div>
                </motion.div>

                {/* Results Count */}
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="mb-6"
                >
                    <p className="text-gray-600">
                        Menampilkan <span className="font-semibold text-green-600">{filteredKegiatan.length}</span> kegiatan
                    </p>
                </motion.div>

                {/* Kegiatan Grid */}
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                >
                    {filteredKegiatan.map((item, index) => (
                        <motion.div
                            key={item.id}
                            variants={itemVariants}
                            whileHover={{ y: -5, scale: 1.02 }}
                            className="bg-white rounded-2xl shadow-lg overflow-hidden cursor-pointer group"
                            onClick={() => openModal(item)}
                        >
                            {/* Image */}
                            <div className="relative h-48 overflow-hidden">
                                {item.foto ? (
                                    <img
                                        src={item.foto}
                                        alt={item.nama_kegiatan}
                                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                    />
                                ) : (
                                    <div className="w-full h-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                                        <div className="text-white text-center">
                                            {getJenisIcon(item.jenis_kegiatan)}
                                            <p className="mt-2 font-medium">Tidak ada gambar</p>
                                        </div>
                                    </div>
                                )}

                                {/* Overlay */}
                                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                    <Eye className="w-8 h-8 text-white" />
                                </div>

                                {/* Badge */}
                                <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium border ${getJenisColor(item.jenis_kegiatan)}`}>
                                    <div className="flex items-center gap-1">
                                        {getJenisIcon(item.jenis_kegiatan)}
                                        {item.jenis_kegiatan.charAt(0).toUpperCase() + item.jenis_kegiatan.slice(1)}
                                    </div>
                                </div>
                            </div>

                            {/* Content */}
                            <div className="p-6">
                                <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-green-600 transition-colors">
                                    {item.nama_kegiatan}
                                </h3>

                                <p className="text-gray-600 mb-4 line-clamp-3">
                                    {item.deskripsi}
                                </p>

                                <div className="space-y-2 text-sm text-gray-500">
                                    <div className="flex items-center gap-2">
                                        <Calendar className="w-4 h-4" />
                                        {item.tanggal_kegiatan}
                                    </div>

                                    {item.lokasi && (
                                        <div className="flex items-center gap-2">
                                            <MapPin className="w-4 h-4" />
                                            {item.lokasi}
                                        </div>
                                    )}

                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        {item.author}
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    ))}
                </motion.div>

                {/* Empty State */}
                {filteredKegiatan.length === 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-center py-16"
                    >
                        <div className="text-gray-400 mb-4">
                            <Search className="w-16 h-16 mx-auto" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-600 mb-2">Tidak ada kegiatan ditemukan</h3>
                        <p className="text-gray-500">Coba ubah filter atau kata kunci pencarian Anda</p>
                    </motion.div>
                )}
            </div>

            {/* Modal for detailed view */}
            <AnimatePresence>
                {isModalOpen && selectedKegiatan && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
                        onClick={closeModal}
                    >
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9, y: 20 }}
                            animate={{ opacity: 1, scale: 1, y: 0 }}
                            exit={{ opacity: 0, scale: 0.9, y: 20 }}
                            className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                            onClick={(e) => e.stopPropagation()}
                        >
                            {/* Modal Header */}
                            <div className="relative">
                                {selectedKegiatan.foto ? (
                                    <img
                                        src={selectedKegiatan.foto}
                                        alt={selectedKegiatan.nama_kegiatan}
                                        className="w-full h-64 object-cover rounded-t-2xl"
                                    />
                                ) : (
                                    <div className="w-full h-64 bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center rounded-t-2xl">
                                        <div className="text-white text-center">
                                            {getJenisIcon(selectedKegiatan.jenis_kegiatan)}
                                            <p className="mt-2 font-medium text-lg">Tidak ada gambar</p>
                                        </div>
                                    </div>
                                )}

                                <button
                                    onClick={closeModal}
                                    className="absolute top-4 right-4 bg-white/90 hover:bg-white rounded-full p-2 transition-colors"
                                >
                                    <ArrowLeft className="w-5 h-5 rotate-45" />
                                </button>

                                <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium border ${getJenisColor(selectedKegiatan.jenis_kegiatan)}`}>
                                    <div className="flex items-center gap-1">
                                        {getJenisIcon(selectedKegiatan.jenis_kegiatan)}
                                        {selectedKegiatan.jenis_kegiatan.charAt(0).toUpperCase() + selectedKegiatan.jenis_kegiatan.slice(1)}
                                    </div>
                                </div>
                            </div>

                            {/* Modal Content */}
                            <div className="p-8">
                                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                    {selectedKegiatan.nama_kegiatan}
                                </h2>

                                <div className="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                                    <div className="flex items-center gap-2">
                                        <Calendar className="w-4 h-4" />
                                        {selectedKegiatan.tanggal_kegiatan}
                                    </div>

                                    {selectedKegiatan.lokasi && (
                                        <div className="flex items-center gap-2">
                                            <MapPin className="w-4 h-4" />
                                            {selectedKegiatan.lokasi}
                                        </div>
                                    )}

                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        {selectedKegiatan.author}
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Clock className="w-4 h-4" />
                                        Dibuat {selectedKegiatan.created_at}
                                    </div>
                                </div>

                                <div className="prose max-w-none">
                                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                                        {selectedKegiatan.deskripsi}
                                    </p>
                                </div>

                                {/* Share Button */}
                                <div className="mt-8 pt-6 border-t border-gray-200">
                                    <button
                                        onClick={() => {
                                            if (navigator.share) {
                                                navigator.share({
                                                    title: selectedKegiatan.nama_kegiatan,
                                                    text: selectedKegiatan.deskripsi,
                                                    url: window.location.href
                                                });
                                            } else {
                                                navigator.clipboard.writeText(window.location.href);
                                                alert('Link berhasil disalin!');
                                            }
                                        }}
                                        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                    >
                                        <Share2 className="w-4 h-4" />
                                        Bagikan
                                    </button>
                                </div>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}