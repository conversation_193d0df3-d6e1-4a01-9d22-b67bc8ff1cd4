import { useState, useEffect } from 'react';
import { usePage, router } from '@inertiajs/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    ArrowLeft,
    Calendar,
    User,
    GraduationCap,
    Globe,
    Search,
    Eye,
    Share2,
    BookOpen,
    Award,
    Palette,
    Music,
    Camera,
    Pen
} from 'lucide-react';

interface KaryaData {
    id: number;
    nama_siswa: string;
    kelas: string;
    judul_karya: string;
    jenis_karya: string;
    tanggal_dibuat: string;
    foto: string | null;
}

interface PageProps {
    karya: KaryaData[];
    [key: string]: any;
}

export default function PageKarya() {
    const { props } = usePage<PageProps>();
    const { karya } = props;

    const [filteredKarya, setFilteredKarya] = useState<KaryaData[]>(karya);
    const [selectedFilter, setSelectedFilter] = useState<string>('semua');
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedKarya, setSelectedKarya] = useState<KaryaData | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    // Filter and search functionality
    useEffect(() => {
        let filtered = karya;

        // Filter by type
        if (selectedFilter !== 'semua') {
            filtered = filtered.filter(item => item.jenis_karya.toLowerCase() === selectedFilter.toLowerCase());
        }

        // Filter by search query
        if (searchQuery) {
            filtered = filtered.filter(item =>
                item.nama_siswa.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.judul_karya.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.kelas.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.jenis_karya.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        setFilteredKarya(filtered);
    }, [selectedFilter, searchQuery, karya]);

    const handleGoBack = () => {
        router.visit('/');
    };

    const getJenisIcon = (jenis: string) => {
        const jenisLower = jenis.toLowerCase();
        if (jenisLower.includes('lukis') || jenisLower.includes('gambar')) {
            return <Palette className="w-5 h-5 text-purple-600" />;
        } else if (jenisLower.includes('musik') || jenisLower.includes('lagu')) {
            return <Music className="w-5 h-5 text-blue-600" />;
        } else if (jenisLower.includes('foto') || jenisLower.includes('photography')) {
            return <Camera className="w-5 h-5 text-green-600" />;
        } else if (jenisLower.includes('puisi') || jenisLower.includes('cerpen') || jenisLower.includes('tulisan')) {
            return <Pen className="w-5 h-5 text-orange-600" />;
        } else {
            return <BookOpen className="w-5 h-5 text-indigo-600" />;
        }
    };

    const getJenisColor = (jenis: string) => {
        const jenisLower = jenis.toLowerCase();
        if (jenisLower.includes('lukis') || jenisLower.includes('gambar')) {
            return 'bg-purple-100 text-purple-800 border-purple-200';
        } else if (jenisLower.includes('musik') || jenisLower.includes('lagu')) {
            return 'bg-blue-100 text-blue-800 border-blue-200';
        } else if (jenisLower.includes('foto') || jenisLower.includes('photography')) {
            return 'bg-green-100 text-green-800 border-green-200';
        } else if (jenisLower.includes('puisi') || jenisLower.includes('cerpen') || jenisLower.includes('tulisan')) {
            return 'bg-orange-100 text-orange-800 border-orange-200';
        } else {
            return 'bg-indigo-100 text-indigo-800 border-indigo-200';
        }
    };

    const openModal = (karya: KaryaData) => {
        setSelectedKarya(karya);
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setSelectedKarya(null);
        setIsModalOpen(false);
    };

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.5
            }
        }
    };

    // Get unique jenis karya for filter buttons
    const jenisKaryaList = [...new Set(karya.map(item => item.jenis_karya))];

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
            {/* Header */}
            <div className="bg-gradient-to-r from-green-900 to-green-800 text-white">
                <div className="max-w-7xl mx-auto px-6 py-8">
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-4 mb-6"
                    >
                        <button
                            onClick={handleGoBack}
                            className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
                        >
                            <ArrowLeft className="w-5 h-5" />
                            Kembali
                        </button>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="text-center"
                    >
                        <h1 className="text-4xl font-bold mb-4">Karya Siswa</h1>
                        <p className="text-xl text-purple-100 max-w-3xl mx-auto">
                            Koleksi karya kreatif dan inovatif dari siswa-siswi MTs Negeri 4 Gunungkidul
                        </p>
                    </motion.div>
                </div>
            </div>

            {/* Filters and Search */}
            <div className="max-w-7xl mx-auto px-6 py-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-white rounded-2xl shadow-lg p-6 mb-8"
                >
                    <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
                        {/* Search */}
                        <div className="relative flex-1 max-w-md">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <input
                                type="text"
                                placeholder="Cari karya siswa..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            />
                        </div>

                        {/* Filter Buttons */}
                        <div className="flex gap-2 flex-wrap">
                            <button
                                onClick={() => setSelectedFilter('semua')}
                                className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all ${
                                    selectedFilter === 'semua'
                                        ? 'bg-purple-600 text-white shadow-lg'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                }`}
                            >
                                <Globe className="w-4 h-4" />
                                Semua
                            </button>
                            {jenisKaryaList.slice(0, 4).map((jenis) => (
                                <button
                                    key={jenis}
                                    onClick={() => setSelectedFilter(jenis)}
                                    className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all ${
                                        selectedFilter === jenis
                                            ? 'bg-purple-600 text-white shadow-lg'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                    }`}
                                >
                                    {getJenisIcon(jenis)}
                                    {jenis}
                                </button>
                            ))}
                        </div>
                    </div>
                </motion.div>

                {/* Stats */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-center mb-8"
                >
                    <p className="text-gray-600">
                        Menampilkan <span className="font-semibold text-purple-600">{filteredKarya.length}</span> dari{' '}
                        <span className="font-semibold">{karya.length}</span> karya siswa
                    </p>
                </motion.div>

                {/* Karya Grid */}
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                >
                    {filteredKarya.map((item, index) => (
                        <motion.div
                            key={item.id}
                            variants={itemVariants}
                            whileHover={{ y: -5, scale: 1.02 }}
                            className="bg-white rounded-2xl shadow-lg overflow-hidden cursor-pointer group"
                            onClick={() => openModal(item)}
                        >
                            {/* Image */}
                            <div className="relative h-48 overflow-hidden">
                                {item.foto ? (
                                    <img
                                        src={item.foto}
                                        alt={item.judul_karya}
                                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                    />
                                ) : (
                                    <div className="w-full h-full bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center">
                                        <div className="text-white text-center">
                                            {getJenisIcon(item.jenis_karya)}
                                            <p className="mt-2 font-medium">Tidak ada gambar</p>
                                        </div>
                                    </div>
                                )}

                                {/* Overlay */}
                                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                    <Eye className="w-8 h-8 text-white" />
                                </div>

                                {/* Badge */}
                                <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium border ${getJenisColor(item.jenis_karya)}`}>
                                    <div className="flex items-center gap-1">
                                        {getJenisIcon(item.jenis_karya)}
                                        {item.jenis_karya}
                                    </div>
                                </div>
                            </div>

                            {/* Content */}
                            <div className="p-6">
                                <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-purple-600 transition-colors">
                                    {item.judul_karya}
                                </h3>

                                <div className="space-y-2 text-sm text-gray-500">
                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        {item.nama_siswa}
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <GraduationCap className="w-4 h-4" />
                                        Kelas {item.kelas}
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Calendar className="w-4 h-4" />
                                        {item.tanggal_dibuat}
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    ))}
                </motion.div>

                {/* Empty State */}
                {filteredKarya.length === 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-center py-16"
                    >
                        <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-600 mb-2">Tidak ada karya ditemukan</h3>
                        <p className="text-gray-500">
                            {searchQuery || selectedFilter !== 'semua'
                                ? 'Coba ubah kata kunci pencarian atau filter'
                                : 'Belum ada karya siswa yang tersedia'}
                        </p>
                    </motion.div>
                )}
            </div>

            {/* Modal for detailed view */}
            <AnimatePresence>
                {isModalOpen && selectedKarya && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
                        onClick={closeModal}
                    >
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9, y: 20 }}
                            animate={{ opacity: 1, scale: 1, y: 0 }}
                            exit={{ opacity: 0, scale: 0.9, y: 20 }}
                            className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                            onClick={(e) => e.stopPropagation()}
                        >
                            {/* Modal Header */}
                            <div className="relative">
                                {selectedKarya.foto ? (
                                    <img
                                        src={selectedKarya.foto}
                                        alt={selectedKarya.judul_karya}
                                        className="w-full h-64 object-cover rounded-t-2xl"
                                    />
                                ) : (
                                    <div className="w-full h-64 bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center rounded-t-2xl">
                                        <div className="text-white text-center">
                                            {getJenisIcon(selectedKarya.jenis_karya)}
                                            <p className="mt-2 font-medium text-lg">Tidak ada gambar</p>
                                        </div>
                                    </div>
                                )}

                                <button
                                    onClick={closeModal}
                                    className="absolute top-4 right-4 bg-white/90 hover:bg-white rounded-full p-2 transition-colors"
                                >
                                    <ArrowLeft className="w-5 h-5 rotate-45" />
                                </button>

                                {/* Badge in modal */}
                                <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium border ${getJenisColor(selectedKarya.jenis_karya)}`}>
                                    <div className="flex items-center gap-1">
                                        {getJenisIcon(selectedKarya.jenis_karya)}
                                        {selectedKarya.jenis_karya}
                                    </div>
                                </div>
                            </div>

                            {/* Modal Content */}
                            <div className="p-8">
                                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                    {selectedKarya.judul_karya}
                                </h2>

                                <div className="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        {selectedKarya.nama_siswa}
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <GraduationCap className="w-4 h-4" />
                                        Kelas {selectedKarya.kelas}
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Calendar className="w-4 h-4" />
                                        {selectedKarya.tanggal_dibuat}
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Award className="w-4 h-4" />
                                        {selectedKarya.jenis_karya}
                                    </div>
                                </div>

                                <div className="prose max-w-none">
                                    <p className="text-gray-700 leading-relaxed">
                                        Karya berjudul "{selectedKarya.judul_karya}" ini merupakan hasil kreativitas dari {selectedKarya.nama_siswa}
                                        kelas {selectedKarya.kelas}. Karya ini termasuk dalam kategori {selectedKarya.jenis_karya.toLowerCase()}
                                        dan dibuat pada tanggal {selectedKarya.tanggal_dibuat}.
                                    </p>
                                </div>

                                {/* Share Button */}
                                <div className="mt-8 pt-6 border-t border-gray-200">
                                    <button
                                        onClick={() => {
                                            if (navigator.share) {
                                                navigator.share({
                                                    title: selectedKarya.judul_karya,
                                                    text: `Karya ${selectedKarya.jenis_karya} oleh ${selectedKarya.nama_siswa}`,
                                                    url: window.location.href
                                                });
                                            } else {
                                                navigator.clipboard.writeText(window.location.href);
                                                alert('Link berhasil disalin!');
                                            }
                                        }}
                                        className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                                    >
                                        <Share2 className="w-4 h-4" />
                                        Bagikan
                                    </button>
                                </div>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}