import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Edit, Trash2, X, Upload, Calendar, MapPin, Users, GraduationCap, Globe } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, router } from '@inertiajs/react';
import { usePage } from '@inertiajs/react';

interface Kegiatan {
    id: number;
    nama_kegiatan: string;
    deskripsi: string;
    foto: string | null;
    tanggal_kegiatan?: string;
    jenis_kegiatan?: 'siswa' | 'guru' | 'umum';
    lokasi?: string;
}



export default function Galeri() {
    const props = usePage().props as unknown as { kegiatan?: any[] };
    const kegiatan = props.kegiatan ?? [];

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<Kegiatan | null>(null);
    const [previewImage, setPreviewImage] = useState<string | null>(null);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        nama_kegiatan: '',
        deskripsi: '',
        foto: null as File | null,
        tanggal_kegiatan: '',
        jenis_kegiatan: 'umum' as 'siswa' | 'guru' | 'umum',
        lokasi: ''
    });

    const openModal = (item?: Kegiatan) => {
        if (item) {
            setEditingItem(item);
            setData({
                nama_kegiatan: item.nama_kegiatan,
                deskripsi: item.deskripsi,
                foto: null,
                tanggal_kegiatan: item.tanggal_kegiatan || '',
                jenis_kegiatan: item.jenis_kegiatan || 'umum',
                lokasi: item.lokasi || ''
            });
            // Set preview image untuk edit mode jika ada foto
            setPreviewImage(item.foto || null);
        } else {
            setEditingItem(null);
            reset();
            setPreviewImage(null);
        }
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
        setEditingItem(null);
        setPreviewImage(null);
        reset();
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            // Validasi ukuran file (maksimal 2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('Ukuran file terlalu besar. Maksimal 2MB.');
                return;
            }

            // Validasi tipe file
            if (!file.type.startsWith('image/')) {
                alert('File harus berupa gambar (PNG, JPG, GIF).');
                return;
            }

            setData('foto', file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setPreviewImage(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (editingItem) {
            put(`/update/kegiatan/${editingItem.id}`, {
                onSuccess: () => closeModal(),
            });
        } else {
            post('/add/kegiatan', {
                onSuccess: () => closeModal(),
            });
        }
    };

    const handleDelete = (id: number) => {
        if (confirm('Apakah Anda yakin ingin menghapus kegiatan ini?')) {
            router.delete(`/delete/kegiatan/${id}`, {
                onSuccess: () => {
                    // Optional: Show success message
                }
            });
        }
    };

    const getJenisIcon = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return <Users className="w-4 h-4 text-blue-600" />;
            case 'guru':
                return <GraduationCap className="w-4 h-4 text-green-600" />;
            default:
                return <Globe className="w-4 h-4 text-purple-600" />;
        }
    };

    const getJenisLabel = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return 'Kegiatan Siswa';
            case 'guru':
                return 'Kegiatan Guru';
            default:
                return 'Kegiatan Umum';
        }
    };

    const getJenisBadgeColor = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return 'bg-blue-100 text-blue-800';
            case 'guru':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-purple-100 text-purple-800';
        }
    };

    return (
        <AppLayout>
            <Head title="Galeri Kegiatan" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        {/* Header */}
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center">
                                <div>
                                    <h2 className="text-2xl font-bold text-gray-900">Galeri Kegiatan</h2>
                                    <p className="text-gray-600 mt-1">Kelola foto dan informasi kegiatan sekolah</p>
                                </div>
                                <motion.button
                                    onClick={() => openModal()}
                                    className="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Tambah Kegiatan
                                </motion.button>
                            </div>
                        </div>

                        {/* Table */}
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Foto
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Nama Kegiatan
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Deskripsi
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Jenis
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Tanggal
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Lokasi
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Aksi
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {kegiatan.length > 0 ? (
                                        kegiatan.map((item, index) => (
                                            <motion.tr
                                                key={item.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                className="hover:bg-gray-50"
                                            >
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex-shrink-0 h-16 w-16">
                                                        {item.foto ? (
                                                            <img
                                                                className="h-16 w-16 rounded-lg object-cover"
                                                                src={item.foto}
                                                                alt={item.nama_kegiatan}
                                                            />
                                                        ) : (
                                                            <div className="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center">
                                                                <Upload className="w-6 h-6 text-gray-400" />
                                                            </div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {item.nama_kegiatan}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="text-sm text-gray-900 max-w-xs truncate">
                                                        {item.deskripsi}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getJenisBadgeColor(item.jenis_kegiatan || 'umum')}`}>
                                                        {getJenisIcon(item.jenis_kegiatan || 'umum')}
                                                        <span className="ml-1">{getJenisLabel(item.jenis_kegiatan || 'umum')}</span>
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <Calendar className="w-4 h-4 mr-1 text-gray-400" />
                                                        {item.tanggal_kegiatan || '-'}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                                                        {item.lokasi || '-'}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div className="flex space-x-2">
                                                        <button
                                                            onClick={() => openModal(item)}
                                                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded-md hover:bg-indigo-50"
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                        </button>
                                                        <button
                                                            onClick={() => handleDelete(item.id)}
                                                            className="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-red-50"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </motion.tr>
                                        ))
                                    ) : (
                                        <tr>
                                            <td colSpan={7} className="px-6 py-12 text-center">
                                                <div className="text-gray-500">
                                                    <Upload className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                                                    <p className="text-lg font-medium">Belum ada kegiatan</p>
                                                    <p className="text-sm">Tambahkan kegiatan pertama Anda</p>
                                                </div>
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Modal */}
            <AnimatePresence>
                {isModalOpen && (
                    <div
                        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
                        onClick={closeModal}
                    >
                        <motion.div
                            className="relative bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
                            initial={{ opacity: 0, scale: 0.95, y: 20 }}
                            animate={{ opacity: 1, scale: 1, y: 0 }}
                            exit={{ opacity: 0, scale: 0.95, y: 20 }}
                            onClick={(e) => e.stopPropagation()}
                        >
                            {/* Close button */}
                            <div className="absolute top-4 right-4 z-10">
                                <button
                                    type="button"
                                    className="bg-white rounded-full p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-lg"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        closeModal();
                                    }}
                                >
                                    <span className="sr-only">Close</span>
                                    <X className="h-5 w-5" />
                                </button>
                            </div>

                                <div className="p-6">
                                    <div className="w-full">
                                        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-6">
                                            {editingItem ? 'Edit Kegiatan' : 'Tambah Kegiatan Baru'}
                                        </h3>

                                        <form onSubmit={handleSubmit} className="space-y-4" style={{ pointerEvents: 'auto' }}>
                                            {/* Upload Foto */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Foto Kegiatan {editingItem && !data.foto && <span className="text-xs text-gray-500">(Opsional - biarkan kosong jika tidak ingin mengubah foto)</span>}
                                                </label>
                                                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                                                    <div className="space-y-1 text-center w-full">
                                                        {previewImage ? (
                                                            <div className="relative inline-block">
                                                                <img
                                                                    src={previewImage}
                                                                    alt="Preview"
                                                                    className="mx-auto h-40 w-40 object-cover rounded-lg shadow-md"
                                                                />
                                                                <button
                                                                    type="button"
                                                                    onClick={() => {
                                                                        setPreviewImage(null);
                                                                        setData('foto', null);
                                                                    }}
                                                                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors shadow-lg"
                                                                >
                                                                    <X className="w-4 h-4" />
                                                                </button>
                                                                <div className="mt-3">
                                                                    <label
                                                                        htmlFor="foto"
                                                                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors"
                                                                    >
                                                                        <Upload className="w-3 h-3 mr-1" />
                                                                        Ganti Foto
                                                                        <input
                                                                            id="foto"
                                                                            name="foto"
                                                                            type="file"
                                                                            className="sr-only"
                                                                            accept="image/*"
                                                                            onChange={handleImageChange}
                                                                        />
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <>
                                                                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                                                <div className="flex text-sm text-gray-600 justify-center">
                                                                    <label
                                                                        htmlFor="foto"
                                                                        className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                                                                    >
                                                                        <span>Upload foto</span>
                                                                        <input
                                                                            id="foto"
                                                                            name="foto"
                                                                            type="file"
                                                                            className="sr-only"
                                                                            accept="image/*"
                                                                            onChange={handleImageChange}
                                                                        />
                                                                    </label>
                                                                    <p className="pl-1">atau drag and drop</p>
                                                                </div>
                                                                <p className="text-xs text-gray-500">PNG, JPG, GIF maksimal 2MB</p>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                                {errors.foto && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.foto}</p>
                                                )}
                                            </div>

                                            {/* Nama Kegiatan */}
                                            <div>
                                                <label htmlFor="nama_kegiatan" className="block text-sm font-medium text-gray-700 mb-1">
                                                    Nama Kegiatan <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    id="nama_kegiatan"
                                                    value={data.nama_kegiatan}
                                                    onChange={(e) => setData('nama_kegiatan', e.target.value)}
                                                    className={`mt-1 block w-full border rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors ${
                                                        errors.nama_kegiatan ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                                                    }`}
                                                    placeholder="Contoh: Upacara Bendera, Lomba Sains, dll."
                                                    required
                                                />
                                                {errors.nama_kegiatan && (
                                                    <p className="mt-1 text-sm text-red-600 flex items-center">
                                                        <X className="w-4 h-4 mr-1" />
                                                        {errors.nama_kegiatan}
                                                    </p>
                                                )}
                                            </div>

                                            {/* Deskripsi */}
                                            <div>
                                                <label htmlFor="deskripsi" className="block text-sm font-medium text-gray-700 mb-1">
                                                    Deskripsi <span className="text-red-500">*</span>
                                                </label>
                                                <textarea
                                                    id="deskripsi"
                                                    rows={4}
                                                    value={data.deskripsi}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        if (value.length <= 500) {
                                                            setData('deskripsi', value);
                                                        }
                                                    }}
                                                    className={`mt-1 block w-full border rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors resize-none ${
                                                        errors.deskripsi ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                                                    }`}
                                                    placeholder="Jelaskan detail kegiatan, tujuan, dan hal-hal penting lainnya..."
                                                    required
                                                    maxLength={500}
                                                />
                                                <p className="mt-1 text-xs text-gray-500">
                                                    {data.deskripsi.length}/500 karakter
                                                </p>
                                                {errors.deskripsi && (
                                                    <p className="mt-1 text-sm text-red-600 flex items-center">
                                                        <X className="w-4 h-4 mr-1" />
                                                        {errors.deskripsi}
                                                    </p>
                                                )}
                                            </div>

                                            {/* Grid untuk form yang lebih compact */}
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                {/* Jenis Kegiatan */}
                                                <div>
                                                    <label htmlFor="jenis_kegiatan" className="block text-sm font-medium text-gray-700 mb-1">
                                                        Jenis Kegiatan <span className="text-red-500">*</span>
                                                    </label>
                                                    <select
                                                        id="jenis_kegiatan"
                                                        value={data.jenis_kegiatan}
                                                        onChange={(e) => setData('jenis_kegiatan', e.target.value as 'siswa' | 'guru' | 'umum')}
                                                        className={`mt-1 block w-full border rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors ${
                                                            errors.jenis_kegiatan ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                                                        }`}
                                                        required
                                                    >
                                                        <option value="umum">🌐 Kegiatan Umum</option>
                                                        <option value="siswa">👥 Kegiatan Siswa</option>
                                                        <option value="guru">🎓 Kegiatan Guru</option>
                                                    </select>
                                                    {errors.jenis_kegiatan && (
                                                        <p className="mt-1 text-sm text-red-600 flex items-center">
                                                            <X className="w-4 h-4 mr-1" />
                                                            {errors.jenis_kegiatan}
                                                        </p>
                                                    )}
                                                </div>

                                                {/* Tanggal Kegiatan */}
                                                <div>
                                                    <label htmlFor="tanggal_kegiatan" className="block text-sm font-medium text-gray-700 mb-1">
                                                        Tanggal Kegiatan
                                                    </label>
                                                    <input
                                                        type="date"
                                                        id="tanggal_kegiatan"
                                                        value={data.tanggal_kegiatan}
                                                        onChange={(e) => setData('tanggal_kegiatan', e.target.value)}
                                                        className={`mt-1 block w-full border rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors ${
                                                            errors.tanggal_kegiatan ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                                                        }`}
                                                    />
                                                    {errors.tanggal_kegiatan && (
                                                        <p className="mt-1 text-sm text-red-600 flex items-center">
                                                            <X className="w-4 h-4 mr-1" />
                                                            {errors.tanggal_kegiatan}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Lokasi */}
                                            <div>
                                                <label htmlFor="lokasi" className="block text-sm font-medium text-gray-700 mb-1">
                                                    Lokasi Kegiatan
                                                </label>
                                                <input
                                                    type="text"
                                                    id="lokasi"
                                                    value={data.lokasi}
                                                    onChange={(e) => setData('lokasi', e.target.value)}
                                                    className={`mt-1 block w-full border rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors ${
                                                        errors.lokasi ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                                                    }`}
                                                    placeholder="Contoh: Aula Sekolah, Lapangan, Ruang Kelas 7A, dll."
                                                />
                                                {errors.lokasi && (
                                                    <p className="mt-1 text-sm text-red-600 flex items-center">
                                                        <X className="w-4 h-4 mr-1" />
                                                        {errors.lokasi}
                                                    </p>
                                                )}
                                            </div>

                                            {/* Submit Buttons */}
                                            <div className="mt-8 pt-6 border-t border-gray-200">
                                                <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0">
                                                    <motion.button
                                                        type="button"
                                                        className="w-full sm:w-auto inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-6 py-2.5 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                                                        onClick={closeModal}
                                                        whileHover={{ scale: 1.02 }}
                                                        whileTap={{ scale: 0.98 }}
                                                    >
                                                        <X className="w-4 h-4 mr-2" />
                                                        Batal
                                                    </motion.button>
                                                    <motion.button
                                                        type="submit"
                                                        disabled={processing || !data.nama_kegiatan.trim() || !data.deskripsi.trim()}
                                                        className={`w-full sm:w-auto inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-6 py-2.5 text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all ${
                                                            processing || !data.nama_kegiatan.trim() || !data.deskripsi.trim()
                                                                ? 'bg-gray-400 cursor-not-allowed'
                                                                : editingItem
                                                                ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                                                                : 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
                                                        }`}
                                                        whileHover={!processing && data.nama_kegiatan.trim() && data.deskripsi.trim() ? { scale: 1.02 } : {}}
                                                        whileTap={!processing && data.nama_kegiatan.trim() && data.deskripsi.trim() ? { scale: 0.98 } : {}}
                                                    >
                                                        {processing ? (
                                                            <>
                                                                <motion.div
                                                                    className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full"
                                                                    animate={{ rotate: 360 }}
                                                                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                                                />
                                                                Menyimpan...
                                                            </>
                                                        ) : editingItem ? (
                                                            <>
                                                                <Edit className="w-4 h-4 mr-2" />
                                                                Update Kegiatan
                                                            </>
                                                        ) : (
                                                            <>
                                                                <Plus className="w-4 h-4 mr-2" />
                                                                Simpan Kegiatan
                                                            </>
                                                        )}
                                                    </motion.button>
                                                </div>
                                                <p className="mt-3 text-xs text-gray-500 text-center">
                                                    <span className="text-red-500">*</span> Field yang wajib diisi
                                                </p>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    )}
            </AnimatePresence>
        </AppLayout>
    );
}